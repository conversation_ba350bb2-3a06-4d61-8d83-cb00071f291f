import { Link } from 'react-router-dom';
import { useState, useEffect } from 'react';

// Import local images
import forestImg from '../../assets/photos/forest.webp';
import darjeelingImg from '../../assets/photos/dargeeling2.webp';
import gangtokImg from '../../assets/photos/city_gangtok.webp';



const HeroSection = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const images = [forestImg, darjeelingImg, gangtokImg];

  const destinations = [
    'Darjeeling',
    'Gangtok',
    'North Sikkim',
    'Pelling',
    'Dooars',
    'Kalimpong',
    'Lachen',
    'Lachung'
  ];

  const filteredDestinations = destinations.filter(dest =>
    dest.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    setIsVisible(true);
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % images.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);



  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setShowDropdown(e.target.value.length > 0);
  };

  const handleDestinationSelect = (destination) => {
    setSearchQuery(destination);
    setShowDropdown(false);
  };

  return (
    <div className="relative min-h-screen bg-gray-50">
      {/* Split Screen Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 min-h-screen">

        {/* Left Side - Content */}
        <div className="flex items-center justify-center p-8 lg:p-16 bg-white relative overflow-hidden">
          {/* Decorative Elements */}
          <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-teal-100 to-cyan-100 rounded-full -translate-x-16 -translate-y-16 opacity-60"></div>
          <div className="absolute bottom-0 right-0 w-24 h-24 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-full translate-x-12 translate-y-12 opacity-40"></div>

          <div className={`max-w-lg transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-50 to-indigo-100 border border-indigo-200 rounded-full text-indigo-700 text-sm font-medium mb-8">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
              </svg>
              Discover North East India
            </div>

            {/* Main Heading */}
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
              Adventure
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-indigo-700">
                Awaits You
              </span>
            </h1>

            {/* Description */}
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              Embark on an extraordinary journey through pristine landscapes, ancient monasteries, and vibrant cultures of India's northeastern paradise.
            </p>

            {/* Search Bar */}
            <div className="relative mb-8">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search destinations..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="w-full px-6 py-4 bg-white border-2 border-gray-200 rounded-2xl text-gray-800 placeholder-gray-500 focus:border-indigo-500 focus:outline-none transition-all duration-300 shadow-lg"
                />
                <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>

              {/* Dropdown */}
              {showDropdown && filteredDestinations.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl z-50 max-h-60 overflow-y-auto">
                  {filteredDestinations.map((destination, index) => (
                    <button
                      key={index}
                      onClick={() => handleDestinationSelect(destination)}
                      className="w-full px-6 py-3 text-left hover:bg-indigo-50 hover:text-indigo-600 transition-colors duration-200 border-b border-gray-100 last:border-b-0"
                    >
                      <div className="flex items-center">
                        <svg className="w-4 h-4 mr-3 text-indigo-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                        </svg>
                        {destination}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <Link
                to="/plan"
                className="group bg-gradient-to-r from-indigo-600 to-indigo-700 text-white px-8 py-4 rounded-2xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:from-indigo-700 hover:to-indigo-800 flex items-center justify-center"
              >
                <svg className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
                Start Your Journey
              </Link>
              <Link
                to="/gallery"
                className="group bg-white border-2 border-gray-200 text-gray-700 px-8 py-4 rounded-2xl font-semibold hover:border-indigo-300 hover:text-indigo-600 transition-all duration-300 flex items-center justify-center"
              >
                <svg className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                View Gallery
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 pt-8 border-t border-gray-100">
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-600 mb-1">50+</div>
                <div className="text-sm text-gray-500">Destinations</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-700 mb-1">1000+</div>
                <div className="text-sm text-gray-500">Happy Travelers</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-600 mb-1">15+</div>
                <div className="text-sm text-gray-500">Years Experience</div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Images */}
        <div className="relative bg-gradient-to-br from-teal-50 to-cyan-50 flex items-center justify-center p-8">
          {/* Image Container */}
          <div className="relative w-full max-w-lg">
            {/* Main Image */}
            <div className="relative overflow-hidden rounded-3xl shadow-2xl">
              <img
                src={images[currentImageIndex]}
                alt="North East India"
                className="w-full h-96 lg:h-[500px] object-cover transition-all duration-1000"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>

            {/* Floating Cards */}
            <div className="absolute -top-6 -left-6 bg-white p-4 rounded-2xl shadow-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-gray-700">Live Adventures</span>
              </div>
            </div>

            <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-2xl shadow-lg">
              <div className="text-center">
                <div className="text-lg font-bold text-indigo-600">4.9★</div>
                <div className="text-xs text-gray-500">Customer Rating</div>
              </div>
            </div>

            {/* Image Indicators */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
              {images.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`w-2 h-2 rounded-full transition-all duration-300 ${
                    index === currentImageIndex ? 'bg-white w-6' : 'bg-white/50'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Background Decorations */}
          <div className="absolute top-20 right-20 w-16 h-16 bg-gradient-to-br from-cyan-200 to-teal-200 rounded-full opacity-60 animate-pulse"></div>
          <div className="absolute bottom-32 left-16 w-12 h-12 bg-gradient-to-br from-teal-200 to-emerald-200 rounded-full opacity-40 animate-pulse" style={{animationDelay: '1s'}}></div>
        </div>
      </div>

      {/* Quick Links Section */}
      <div className="bg-white py-8 border-t border-gray-100">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-6">
            <h3 className="text-lg font-semibold text-gray-800">Popular Destinations</h3>
          </div>
          <div className="flex justify-center items-center flex-wrap gap-8">
            <Link
              to="/plan?destination=darjeeling"
              className="group flex items-center text-gray-700 hover:text-indigo-600 font-medium transition-all duration-300"
            >
              <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3 group-hover:bg-indigo-500 transition-all duration-300">
                <svg className="w-5 h-5 text-indigo-600 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                </svg>
              </div>
              <span>Darjeeling</span>
            </Link>

            <Link
              to="/plan?destination=gangtok"
              className="group flex items-center text-gray-700 hover:text-indigo-600 font-medium transition-all duration-300"
            >
              <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3 group-hover:bg-indigo-500 transition-all duration-300">
                <svg className="w-5 h-5 text-indigo-600 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" clipRule="evenodd" />
                </svg>
              </div>
              <span>Gangtok</span>
            </Link>

            <Link
              to="/plan?destination=sikkim"
              className="group flex items-center text-gray-700 hover:text-indigo-600 font-medium transition-all duration-300"
            >
              <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3 group-hover:bg-indigo-500 transition-all duration-300">
                <svg className="w-5 h-5 text-indigo-600 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z" clipRule="evenodd" />
                </svg>
              </div>
              <span>North Sikkim</span>
            </Link>

            <Link
              to="/plan?destination=pelling"
              className="group flex items-center text-gray-700 hover:text-indigo-600 font-medium transition-all duration-300"
            >
              <div className="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3 group-hover:bg-indigo-500 transition-all duration-300">
                <svg className="w-5 h-5 text-indigo-600 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
              </div>
              <span>Pelling</span>
            </Link>

            <Link
              to="/plan"
              className="group flex items-center text-indigo-600 hover:text-indigo-700 font-semibold transition-all duration-300"
            >
              <span>View All Destinations</span>
              <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
